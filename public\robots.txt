# Code Guardian Enterprise - Professional Robots.txt
# https://code-guardian-report.vercel.app/robots.txt

User-agent: *
Allow: /

# Sitemap location
Sitemap: https://code-guardian-report.vercel.app/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Specific bot configurations
User-agent: Googlebot
Allow: /
Crawl-delay: 0

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# Block AI training bots (optional - for content protection)
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: Claude-Web
Disallow: /

# Block common spam/scraper bots
User-agent: SemrushBot
Disallow: /

User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

# Allow social media bots for rich previews
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

User-agent: TelegramBot
Allow: /

# Security and performance
User-agent: *
Disallow: /api/
Disallow: /.well-known/
Disallow: /admin/
Disallow: /private/
Disallow: /temp/
Disallow: /cache/
Disallow: /*.json$
Disallow: /*.xml$
Disallow: /*?*utm_*
Disallow: /*?*ref=*
Disallow: /*?*fbclid=*
Disallow: /*?*gclid=*

# Allow important files
Allow: /manifest.json
Allow: /sitemap.xml
Allow: /favicon.ico
Allow: /*.css
Allow: /*.js
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.gif
Allow: /*.svg
Allow: /*.webp
Allow: /*.woff
Allow: /*.woff2

# Host information
Host: https://code-guardian-report.vercel.app