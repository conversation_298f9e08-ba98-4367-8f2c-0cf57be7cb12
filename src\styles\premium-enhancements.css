/* Premium Enterprise-Grade Visual Enhancements */

@layer utilities {
  /* Ultra-Premium Gradient System */
  .gradient-enterprise {
    background: linear-gradient(135deg, 
      #667eea 0%, 
      #764ba2 15%, 
      #f093fb 30%, 
      #4fac<PERSON> 45%, 
      #00f2fe 60%, 
      #43e97b 75%, 
      #52a9ff 90%, 
      #667eea 100%);
    background-size: 400% 400%;
    animation: gradient-enterprise 8s ease infinite;
  }

  @keyframes gradient-enterprise {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
  }

  .gradient-text-enterprise {
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 25%, #8b5cf6 50%, #ec4899 75%, #f59e0b 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-text-flow 6s ease infinite;
  }

  .dark .gradient-text-enterprise {
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  @keyframes gradient-text-flow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Ultra-Modern Glass Morphism */
  .glass-ultra {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(32px);
    -webkit-backdrop-filter: blur(32px);
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  }

  .dark .glass-ultra {
    background: rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Premium Neumorphism */
  .neomorphism {
    background: linear-gradient(145deg, #f0f4f8, #e2e8f0);
    box-shadow: 
      20px 20px 40px #d1d9e0,
      -20px -20px 40px #ffffff,
      inset 0 0 0 1px rgba(255, 255, 255, 0.5);
    border: none;
  }

  .dark .neomorphism {
    background: linear-gradient(145deg, #1e293b, #0f172a);
    box-shadow: 
      20px 20px 40px #0a0f1a,
      -20px -20px 40px #2d3748,
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Enterprise-Grade Shadows */
  .shadow-enterprise {
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-enterprise-lg {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .shadow-glow-blue {
    box-shadow: 
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.2),
      0 0 80px rgba(59, 130, 246, 0.1);
  }

  .shadow-glow-purple {
    box-shadow: 
      0 0 20px rgba(139, 92, 246, 0.3),
      0 0 40px rgba(139, 92, 246, 0.2),
      0 0 80px rgba(139, 92, 246, 0.1);
  }

  /* Ultra-Premium Buttons */
  .btn-enterprise {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    border: none;
    color: white;
    font-weight: 700;
    letter-spacing: 0.025em;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 
      0 10px 25px rgba(102, 126, 234, 0.3),
      0 5px 10px rgba(102, 126, 234, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .btn-enterprise::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .btn-enterprise:hover {
    transform: translateY(-2px) scale(1.05);
    background-position: 100% 0;
    box-shadow: 
      0 20px 40px rgba(102, 126, 234, 0.4),
      0 10px 20px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .btn-enterprise:hover::before {
    left: 100%;
  }

  .btn-enterprise:active {
    transform: translateY(-1px) scale(1.02);
  }

  /* Premium Card Variants */
  .card-enterprise {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
  }

  .dark .card-enterprise {
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .card-enterprise::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    opacity: 0;
    transition: opacity 0.6s ease;
  }

  .card-enterprise:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
      0 20px 60px rgba(0, 0, 0, 0.15),
      0 8px 24px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .card-enterprise:hover::before {
    opacity: 1;
  }

  /* Premium Navigation - Completely Solid Background */
  .portal-navbar,
  .nav-enterprise {
    background-color: #ffffff !important;
    background: #ffffff !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border-bottom: 1px solid #e2e8f0 !important;
    box-shadow: 
      0 4px 24px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);
    opacity: 1 !important;
  }

  .dark .portal-navbar,
  .dark .nav-enterprise {
    background-color: #0f172a !important;
    background: #0f172a !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border-bottom: 1px solid #334155 !important;
    box-shadow: 
      0 4px 24px rgba(0, 0, 0, 0.3),
      0 2px 8px rgba(0, 0, 0, 0.15);
    opacity: 1 !important;
  }

  /* Ultra-Modern Animations */
  @keyframes float-enterprise {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg);
      opacity: 0.8;
    }
    25% { 
      transform: translateY(-10px) rotate(1deg);
      opacity: 0.9;
    }
    50% { 
      transform: translateY(-20px) rotate(0deg);
      opacity: 1;
    }
    75% { 
      transform: translateY(-10px) rotate(-1deg);
      opacity: 0.9;
    }
  }

  .animate-float-enterprise {
    animation: float-enterprise 6s ease-in-out infinite;
  }

  @keyframes pulse-enterprise {
    0%, 100% { 
      opacity: 0.6;
      transform: scale(1);
    }
    50% { 
      opacity: 1;
      transform: scale(1.05);
    }
  }

  .animate-pulse-enterprise {
    animation: pulse-enterprise 4s ease-in-out infinite;
  }

  /* Premium Input Fields */
  .input-enterprise {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .dark .input-enterprise {
    background: rgba(15, 23, 42, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.1);
    color: white;
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .input-enterprise:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 
      0 8px 32px rgba(59, 130, 246, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
  }

  /* Premium Loading States */
  .loading-enterprise {
    position: relative;
    overflow: hidden;
  }

  .loading-enterprise::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: loading-shimmer 2s infinite;
  }

  @keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Responsive Enhancements */
  @media (max-width: 768px) {
    .card-enterprise {
      border-radius: 20px;
      margin: 8px;
    }
    
    .btn-enterprise {
      padding: 14px 24px;
      font-size: 16px;
      border-radius: 16px;
    }
    
    .input-enterprise {
      padding: 14px 18px;
      border-radius: 14px;
    }
  }

  @media (max-width: 640px) {
    .card-enterprise {
      border-radius: 16px;
      margin: 4px;
    }
    
    .btn-enterprise {
      padding: 12px 20px;
      font-size: 15px;
      border-radius: 14px;
    }
  }

  /* Premium Focus States */
  .focus-enterprise {
    outline: none;
    box-shadow: 
      0 0 0 3px rgba(59, 130, 246, 0.1),
      0 0 0 6px rgba(59, 130, 246, 0.05),
      0 8px 32px rgba(59, 130, 246, 0.2);
  }

  /* Ultra-Modern Typography */
  .text-enterprise {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1, "ss01" 1;
    letter-spacing: -0.025em;
    line-height: 1.5;
  }

  .text-enterprise-heading {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1, "ss01" 1;
    font-weight: 800;
    letter-spacing: -0.05em;
    line-height: 1.2;
  }
}