/**
 * Enhanced Vulnerability Detection Service
 * Provides real vulnerability detection with detailed analysis and remediation
 */

import { SecurityIssue } from '@/hooks/useAnalysis';

export interface VulnerabilityPattern {
  id: string;
  name: string;
  pattern: RegExp;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  confidence: number;
  cweId: string;
  description: string;
  impact: string;
  remediation: string;
  examples: {
    vulnerable: string;
    secure: string;
  };
  languages: string[];
  frameworks?: string[];
}

export interface VulnerabilityDetectionResult {
  vulnerabilities: SecurityIssue[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  riskScore: number;
  recommendations: string[];
}

export class EnhancedVulnerabilityDetector {
  private vulnerabilityPatterns: VulnerabilityPattern[] = [];

  constructor() {
    this.initializeVulnerabilityPatterns();
  }

  /**
   * Initialize comprehensive vulnerability patterns
   */
  private initializeVulnerabilityPatterns(): void {
    this.vulnerabilityPatterns = [
      // SQL Injection Patterns
      {
        id: 'sql-injection-concat',
        name: 'SQL Injection via String Concatenation',
        pattern: /(query|execute|exec)\s*\(\s*["'`][^"'`]*["'`]\s*\+\s*\w+/gi,
        severity: 'Critical',
        confidence: 95,
        cweId: 'CWE-89',
        description: 'SQL query constructed using string concatenation with user input',
        impact: 'Attackers can execute arbitrary SQL commands, potentially accessing or modifying sensitive data',
        remediation: 'Use parameterized queries or prepared statements',
        examples: {
          vulnerable: 'query("SELECT * FROM users WHERE id = " + userId)',
          secure: 'query("SELECT * FROM users WHERE id = ?", [userId])'
        },
        languages: ['javascript', 'typescript', 'python', 'java', 'php', 'csharp']
      },
      {
        id: 'sql-injection-template',
        name: 'SQL Injection via Template Literals',
        pattern: /query\s*\(\s*`[^`]*\$\{[^}]+\}[^`]*`\s*\)/gi,
        severity: 'Critical',
        confidence: 90,
        cweId: 'CWE-89',
        description: 'SQL query using template literals with embedded variables',
        impact: 'Template literal interpolation can lead to SQL injection vulnerabilities',
        remediation: 'Use parameterized queries instead of template literals for SQL',
        examples: {
          vulnerable: 'query(`SELECT * FROM users WHERE name = ${userName}`)',
          secure: 'query("SELECT * FROM users WHERE name = ?", [userName])'
        },
        languages: ['javascript', 'typescript']
      },

      // XSS Patterns
      {
        id: 'xss-innerhtml',
        name: 'XSS via innerHTML Assignment',
        pattern: /\.innerHTML\s*=\s*[^;]+(?:req\.|request\.|params\.|query\.|body\.|\$_GET|\$_POST|input|user)/gi,
        severity: 'High',
        confidence: 85,
        cweId: 'CWE-79',
        description: 'Direct assignment of user input to innerHTML property',
        impact: 'Allows execution of malicious scripts in user browsers',
        remediation: 'Use textContent or proper HTML sanitization',
        examples: {
          vulnerable: 'element.innerHTML = userInput',
          secure: 'element.textContent = userInput'
        },
        languages: ['javascript', 'typescript']
      },
      {
        id: 'xss-document-write',
        name: 'XSS via document.write',
        pattern: /document\.write\s*\(\s*[^)]*(?:req\.|request\.|params\.|query\.|body\.|\$_GET|\$_POST|input|user)/gi,
        severity: 'High',
        confidence: 90,
        cweId: 'CWE-79',
        description: 'Using document.write with user-controlled input',
        impact: 'Can inject malicious scripts into the document',
        remediation: 'Avoid document.write or sanitize input properly',
        examples: {
          vulnerable: 'document.write("<div>" + userInput + "</div>")',
          secure: 'element.textContent = userInput'
        },
        languages: ['javascript', 'typescript']
      },

      // Command Injection Patterns
      {
        id: 'command-injection-exec',
        name: 'Command Injection via exec/system calls',
        pattern: /(exec|system|shell_exec|passthru|popen|proc_open)\s*\(\s*[^)]*(?:req\.|request\.|params\.|query\.|body\.|\$_GET|\$_POST|input|user)/gi,
        severity: 'Critical',
        confidence: 95,
        cweId: 'CWE-78',
        description: 'Executing system commands with user-controlled input',
        impact: 'Allows arbitrary command execution on the server',
        remediation: 'Validate and sanitize input, use safe APIs instead of shell commands',
        examples: {
          vulnerable: 'exec("ls " + userInput)',
          secure: 'execFile("ls", [userInput], options)'
        },
        languages: ['javascript', 'typescript', 'python', 'php', 'ruby']
      },

      // Path Traversal Patterns
      {
        id: 'path-traversal-file-access',
        name: 'Path Traversal in File Operations',
        pattern: /(readFile|writeFile|open|fopen|file_get_contents|include|require)\s*\(\s*[^)]*(?:req\.|request\.|params\.|query\.|body\.|\$_GET|\$_POST|input|user)/gi,
        severity: 'High',
        confidence: 80,
        cweId: 'CWE-22',
        description: 'File operations using user-controlled paths',
        impact: 'Allows access to files outside intended directory',
        remediation: 'Validate and sanitize file paths, use path.resolve() and check boundaries',
        examples: {
          vulnerable: 'readFile(userPath)',
          secure: 'readFile(path.resolve(basePath, path.normalize(userPath)))'
        },
        languages: ['javascript', 'typescript', 'python', 'php', 'java']
      },

      // Insecure Randomness
      {
        id: 'weak-random-math',
        name: 'Weak Random Number Generation',
        pattern: /Math\.random\(\)|random\(\)|rand\(\)/gi,
        severity: 'Medium',
        confidence: 70,
        cweId: 'CWE-338',
        description: 'Using weak random number generators for security purposes',
        impact: 'Predictable random values can be exploited by attackers',
        remediation: 'Use cryptographically secure random number generators',
        examples: {
          vulnerable: 'const token = Math.random().toString(36)',
          secure: 'const token = crypto.getRandomValues(new Uint8Array(32))'
        },
        languages: ['javascript', 'typescript', 'python', 'php', 'java']
      },

      // Eval Usage
      {
        id: 'dangerous-eval',
        name: 'Dangerous eval() Usage',
        pattern: /eval\s*\(\s*[^)]*(?:req\.|request\.|params\.|query\.|body\.|\$_GET|\$_POST|input|user)/gi,
        severity: 'Critical',
        confidence: 98,
        cweId: 'CWE-95',
        description: 'Using eval() with user-controlled input',
        impact: 'Allows arbitrary code execution',
        remediation: 'Never use eval() with user input, use JSON.parse() for data',
        examples: {
          vulnerable: 'eval(userInput)',
          secure: 'JSON.parse(userInput)'
        },
        languages: ['javascript', 'typescript', 'python', 'php']
      },

      // Hardcoded Credentials
      {
        id: 'hardcoded-password',
        name: 'Hardcoded Password',
        pattern: /(password|passwd|pwd|secret|key)\s*[:=]\s*["'][^"']{8,}["']/gi,
        severity: 'High',
        confidence: 75,
        cweId: 'CWE-798',
        description: 'Hardcoded password or secret in source code',
        impact: 'Credentials exposed in source code can be discovered by attackers',
        remediation: 'Store credentials in environment variables or secure configuration',
        examples: {
          vulnerable: 'const password = "mySecretPassword123"',
          secure: 'const password = process.env.DB_PASSWORD'
        },
        languages: ['javascript', 'typescript', 'python', 'java', 'php', 'csharp']
      },

      // Insecure HTTP
      {
        id: 'insecure-http-request',
        name: 'Insecure HTTP Request',
        pattern: /http:\/\/[^"'\s]+/gi,
        severity: 'Medium',
        confidence: 60,
        cweId: 'CWE-319',
        description: 'Using insecure HTTP instead of HTTPS',
        impact: 'Data transmitted over HTTP can be intercepted',
        remediation: 'Use HTTPS for all external communications',
        examples: {
          vulnerable: 'fetch("http://api.example.com/data")',
          secure: 'fetch("https://api.example.com/data")'
        },
        languages: ['javascript', 'typescript', 'python', 'java', 'php', 'csharp']
      },

      // Regex DoS
      {
        id: 'regex-dos',
        name: 'Regular Expression Denial of Service',
        pattern: /new\s+RegExp\s*\([^)]*\+[^)]*\)|\/.*\(\.\*\+.*\)\+.*\//gi,
        severity: 'Medium',
        confidence: 65,
        cweId: 'CWE-1333',
        description: 'Regular expression vulnerable to catastrophic backtracking',
        impact: 'Can cause application to hang or consume excessive CPU',
        remediation: 'Avoid nested quantifiers and use atomic groups',
        examples: {
          vulnerable: '/^(a+)+$/',
          secure: '/^a+$/'
        },
        languages: ['javascript', 'typescript', 'python', 'java', 'php']
      }
    ];
  }

  /**
   * Detect vulnerabilities in code content
   */
  public detectVulnerabilities(
    content: string, 
    filename: string, 
    language: string
  ): VulnerabilityDetectionResult {
    console.log(`🔍 ENHANCED VULNERABILITY DETECTION: Analyzing ${filename} for ${language} vulnerabilities...`);
    
    const vulnerabilities: SecurityIssue[] = [];
    const lines = content.split('\n');

    // Filter patterns by language
    const applicablePatterns = this.vulnerabilityPatterns.filter(pattern => 
      pattern.languages.includes(language.toLowerCase())
    );

    console.log(`📋 ENHANCED DETECTION: Using ${applicablePatterns.length} patterns for ${language}`);

    // Analyze each line for vulnerability patterns
    lines.forEach((line, lineIndex) => {
      applicablePatterns.forEach(pattern => {
        const matches = line.matchAll(pattern.pattern);
        
        for (const match of matches) {
          const vulnerability = this.createVulnerabilityIssue(
            pattern,
            match,
            filename,
            lineIndex + 1,
            line,
            content
          );
          vulnerabilities.push(vulnerability);
        }
      });
    });

    // Calculate summary and risk score
    const summary = this.calculateSummary(vulnerabilities);
    const riskScore = this.calculateRiskScore(vulnerabilities);
    const recommendations = this.generateRecommendations(vulnerabilities);

    console.log(`✅ ENHANCED DETECTION COMPLETE: Found ${vulnerabilities.length} vulnerabilities in ${filename}`);
    console.log(`📊 ENHANCED DETECTION: Risk Score: ${riskScore}/100`);

    return {
      vulnerabilities,
      summary,
      riskScore,
      recommendations
    };
  }

  /**
   * Create a security issue from a vulnerability pattern match
   */
  private createVulnerabilityIssue(
    pattern: VulnerabilityPattern,
    match: RegExpMatchArray,
    filename: string,
    line: number,
    lineContent: string,
    fullContent: string
  ): SecurityIssue {
    const id = `${pattern.id}-${filename}-${line}-${Date.now()}`;
    
    return {
      id,
      line,
      column: (match.index || 0) + 1,
      tool: 'Enhanced Vulnerability Detector',
      type: pattern.name,
      category: 'Security Vulnerability',
      message: pattern.description,
      severity: pattern.severity,
      confidence: pattern.confidence,
      cvssScore: this.calculateCVSSScore(pattern.severity),
      cweId: pattern.cweId,
      recommendation: pattern.remediation,
      remediation: {
        description: pattern.remediation,
        codeExample: pattern.examples.vulnerable,
        fixExample: pattern.examples.secure,
        effort: this.getEffortLevel(pattern.severity),
        priority: this.getPriorityLevel(pattern.severity)
      },
      filename,
      codeSnippet: this.extractCodeSnippet(fullContent, line),
      riskRating: this.calculateRiskRating(pattern.severity, pattern.confidence),
      impact: pattern.impact,
      likelihood: 'High',
      references: this.generateReferences(pattern.cweId),
      tags: this.generateTags(pattern)
    };
  }

  /**
   * Extract code snippet around the vulnerable line
   */
  private extractCodeSnippet(content: string, lineNumber: number, contextLines: number = 3): string {
    const lines = content.split('\n');
    const start = Math.max(0, lineNumber - contextLines - 1);
    const end = Math.min(lines.length, lineNumber + contextLines);
    
    return lines.slice(start, end)
      .map((line, index) => {
        const actualLineNumber = start + index + 1;
        const marker = actualLineNumber === lineNumber ? '>>> ' : '    ';
        return `${marker}${actualLineNumber}: ${line}`;
      })
      .join('\n');
  }

  /**
   * Calculate CVSS score based on severity
   */
  private calculateCVSSScore(severity: string): number {
    switch (severity) {
      case 'Critical': return 9.5;
      case 'High': return 7.5;
      case 'Medium': return 5.0;
      case 'Low': return 2.5;
      default: return 0.0;
    }
  }

  /**
   * Get effort level for remediation
   */
  private getEffortLevel(severity: string): 'Low' | 'Medium' | 'High' {
    switch (severity) {
      case 'Critical': return 'High';
      case 'High': return 'Medium';
      case 'Medium': return 'Low';
      case 'Low': return 'Low';
      default: return 'Medium';
    }
  }

  /**
   * Get priority level for remediation
   */
  private getPriorityLevel(severity: string): number {
    switch (severity) {
      case 'Critical': return 5;
      case 'High': return 4;
      case 'Medium': return 3;
      case 'Low': return 2;
      default: return 1;
    }
  }

  /**
   * Calculate risk rating
   */
  private calculateRiskRating(severity: string, confidence: number): 'Critical' | 'High' | 'Medium' | 'Low' {
    const severityWeight = this.getSeverityWeight(severity);
    const confidenceWeight = confidence / 100;
    const riskScore = severityWeight * confidenceWeight;

    if (riskScore >= 0.8) return 'Critical';
    if (riskScore >= 0.6) return 'High';
    if (riskScore >= 0.4) return 'Medium';
    return 'Low';
  }

  /**
   * Get severity weight for calculations
   */
  private getSeverityWeight(severity: string): number {
    switch (severity) {
      case 'Critical': return 1.0;
      case 'High': return 0.75;
      case 'Medium': return 0.5;
      case 'Low': return 0.25;
      default: return 0.1;
    }
  }

  /**
   * Generate references for CWE
   */
  private generateReferences(cweId: string): string[] {
    return [
      `https://cwe.mitre.org/data/definitions/${cweId.replace('CWE-', '')}.html`,
      'https://owasp.org/www-project-top-ten/',
      'https://cheatsheetseries.owasp.org/'
    ];
  }

  /**
   * Generate tags for vulnerability
   */
  private generateTags(pattern: VulnerabilityPattern): string[] {
    const tags = ['vulnerability', pattern.severity.toLowerCase()];
    
    if (pattern.cweId.includes('89')) tags.push('sql-injection');
    if (pattern.cweId.includes('79')) tags.push('xss');
    if (pattern.cweId.includes('78')) tags.push('command-injection');
    if (pattern.cweId.includes('22')) tags.push('path-traversal');
    
    return tags;
  }

  /**
   * Calculate vulnerability summary
   */
  private calculateSummary(vulnerabilities: SecurityIssue[]) {
    return {
      total: vulnerabilities.length,
      critical: vulnerabilities.filter(v => v.severity === 'Critical').length,
      high: vulnerabilities.filter(v => v.severity === 'High').length,
      medium: vulnerabilities.filter(v => v.severity === 'Medium').length,
      low: vulnerabilities.filter(v => v.severity === 'Low').length
    };
  }

  /**
   * Calculate overall risk score
   */
  private calculateRiskScore(vulnerabilities: SecurityIssue[]): number {
    if (vulnerabilities.length === 0) return 100;

    const weights = { Critical: 40, High: 20, Medium: 10, Low: 5 };
    const totalPenalty = vulnerabilities.reduce((sum, vuln) => {
      return sum + (weights[vuln.severity as keyof typeof weights] || 0);
    }, 0);

    return Math.max(0, 100 - totalPenalty);
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(vulnerabilities: SecurityIssue[]): string[] {
    const recommendations = new Set<string>();

    vulnerabilities.forEach(vuln => {
      if (vuln.type.includes('SQL')) {
        recommendations.add('Implement parameterized queries for all database operations');
      }
      if (vuln.type.includes('XSS')) {
        recommendations.add('Implement proper output encoding and input validation');
      }
      if (vuln.type.includes('Command')) {
        recommendations.add('Avoid system command execution with user input');
      }
      if (vuln.type.includes('Path')) {
        recommendations.add('Implement strict path validation and sanitization');
      }
      if (vuln.type.includes('Random')) {
        recommendations.add('Use cryptographically secure random number generators');
      }
    });

    if (recommendations.size === 0) {
      recommendations.add('Continue following secure coding practices');
    }

    return Array.from(recommendations);
  }
}
