{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@services/*": ["./src/services/*"], "@hooks/*": ["./src/hooks/*"], "@utils/*": ["./src/utils/*"], "@styles/*": ["./src/styles/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "vite.config.ts", "vitest.config.ts", "tailwind.config.ts"], "exclude": ["node_modules", "dist", "build", "coverage"]}