<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 310 150" width="310" height="150">
  <defs>
    <linearGradient id="shieldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4338ca;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="highlightGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>
  </defs>
  <!-- Main shield body with blue-to-indigo gradient matching navbar -->
  <rect x="125" y="10" width="60" height="130" rx="10" fill="url(#shieldGrad)" />
  
  <!-- Shield icon (simplified) -->
  <path d="M155 35 L135 50 L135 90 Q135 110 155 125 Q175 110 175 90 L175 50 Z" fill="white" />
  
  <!-- Highlight overlay for 3D effect -->
  <rect x="125" y="10" width="60" height="130" rx="10" fill="url(#highlightGrad)" />
</svg>