import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shield, Heart, Mail, Github, Globe, Scale, FileText, Code, Building2, Brain, Star, Users, Twitter, Linkedin, ExternalLink } from 'lucide-react';
import { APP_VERSION_WITH_PREFIX } from '@/utils/version';

interface FooterProps {
  className?: string;
}

export const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    main: [
      { path: '/', label: 'AI Platform', description: 'Next-gen security analysis' },
      { path: '/about', label: 'About', description: 'Enterprise tools' }
    ],
    legal: [
      { path: '/privacy', label: 'Privacy Policy', description: 'Data protection' },
      { path: '/terms', label: 'Terms of Service', description: 'Usage terms' }
    ],
    resources: [
      { path: '#documentation', label: 'Documentation', description: 'User guides' },
      { path: '#api-reference', label: 'API Reference', description: 'Developer docs' },
      { path: '#community', label: 'Community', description: 'Join developers' },
      { path: '#support', label: 'Support', description: 'Get help' }
    ]
  };

  const socialLinks = [
    { name: 'GitHub', icon: Github, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'LinkedIn', icon: Linkedin, href: '#' }
  ];

  return (
    <footer className={`bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-800 ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                  Code Guardian
                </h3>
                <p className="text-sm text-slate-600 dark:text-slate-400">AI-Powered Security Platform</p>
              </div>
            </div>
            
            <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md">
              Revolutionizing code security with advanced AI analysis, real-time threat detection, and comprehensive vulnerability assessment.
            </p>

            {/* Social Links */}
            <div className="flex items-center gap-3">
              {socialLinks.map((social) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="p-2 rounded-lg bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition-all duration-200"
                    aria-label={social.name}
                  >
                    <IconComponent className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
              <Brain className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              Platform
            </h4>
            <ul className="space-y-3">
              {footerLinks.main.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Links */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
              <FileText className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
              Resources
            </h4>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.path}>
                  <a
                    href={link.path}
                    className="text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors duration-200 flex items-center gap-1"
                  >
                    {link.label}
                    {link.path === '#' && <ExternalLink className="h-3 w-3" />}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
              <Scale className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              Legal
            </h4>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-slate-200 dark:border-slate-700 pt-8">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Copyright and Trust Badge */}
            <div className="flex flex-col sm:flex-row items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
              <div className="flex items-center gap-2">
                <Heart className="h-4 w-4 text-red-500" />
                <span>© {currentYear} Code Guardian. Made with love for developers.</span>
              </div>
              <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-blue-900 dark:text-blue-100 font-medium">Trusted by 10K+ developers</span>
              </div>
            </div>

            {/* Contact and Version */}
            <div className="flex items-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 px-4 py-2 rounded-lg bg-emerald-50 dark:bg-emerald-950/30 border border-emerald-200 dark:border-emerald-800 text-emerald-900 dark:text-emerald-100 hover:bg-emerald-100 dark:hover:bg-emerald-950/50 transition-colors duration-200"
              >
                <Mail className="h-4 w-4" />
                <span className="text-sm font-medium">Contact</span>
              </a>
              <div className="flex items-center gap-2 px-3 py-1 rounded-lg bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                <Code className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                <span className="text-sm text-slate-700 dark:text-slate-300">{APP_VERSION_WITH_PREFIX}</span>
              </div>
            </div>
          </div>

          {/* Compliance Badges */}
          <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
            <div className="flex flex-wrap items-center justify-center gap-6 text-xs text-slate-500 dark:text-slate-500">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <span>Global Security Platform</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>SOC 2 Type II Compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>ISO 27001 Certified</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;