# Code Guardian Enterprise - Environment Configuration
# Copy this file to .env.local and configure your values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
VITE_APP_NAME="Code Guardian Enterprise"
VITE_APP_VERSION="4.5.0"
VITE_APP_DESCRIPTION="Enterprise-grade static code analysis platform powered by artificial intelligence"
VITE_APP_URL="https://code-guardian-report.vercel.app"
VITE_APP_ENVIRONMENT="production"

# =============================================================================
# AI PROVIDER CONFIGURATION
# =============================================================================

# OpenAI Configuration
VITE_OPENAI_API_URL="https://api.openai.com/v1"
VITE_OPENAI_API_KEY="your_openai_api_key_here"
VITE_OPENAI_MODEL="gpt-4-turbo-preview"
VITE_OPENAI_MAX_TOKENS="4096"

# Anthropic Claude Configuration
VITE_ANTHROPIC_API_URL="https://api.anthropic.com/v1"
VITE_ANTHROPIC_API_KEY="your_anthropic_api_key_here"
VITE_ANTHROPIC_MODEL="claude-3-sonnet-20240229"
VITE_ANTHROPIC_MAX_TOKENS="4096"

# Google Gemini Configuration
VITE_GEMINI_API_URL="https://generativelanguage.googleapis.com/v1beta"
VITE_GEMINI_API_KEY="your_gemini_api_key_here"
VITE_GEMINI_MODEL="gemini-pro"

# OpenRouter Configuration
VITE_OPENROUTER_API_URL="https://openrouter.ai/api/v1"
VITE_OPENROUTER_API_KEY="your_openrouter_api_key_here"
VITE_OPENROUTER_MODEL="openai/gpt-4o-mini"

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API Security
VITE_API_RATE_LIMIT="100"
VITE_API_TIMEOUT="30000"
VITE_ENABLE_CORS="true"

# Content Security Policy
VITE_CSP_ENABLED="true"
VITE_CSP_REPORT_URI="/api/csp-report"

# Security Headers
VITE_SECURITY_HEADERS_ENABLED="true"
VITE_HSTS_MAX_AGE="31536000"

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# Vercel Analytics
VITE_VERCEL_ANALYTICS_ENABLED="true"
VITE_VERCEL_ANALYTICS_DEBUG="false"

# Performance Monitoring
VITE_PERFORMANCE_MONITORING="true"
VITE_ERROR_REPORTING="true"
VITE_LIGHTHOUSE_CI="true"

# User Analytics (Privacy-compliant)
VITE_ANALYTICS_ENABLED="true"
VITE_ANALYTICS_ANONYMIZE_IP="true"
VITE_ANALYTICS_RESPECT_DNT="true"

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Features
VITE_FEATURE_AI_ANALYSIS="true"
VITE_FEATURE_BULK_UPLOAD="true"
VITE_FEATURE_EXPORT_REPORTS="true"
VITE_FEATURE_DARK_MODE="true"

# Advanced Features
VITE_FEATURE_REAL_TIME_ANALYSIS="true"
VITE_FEATURE_COLLABORATION="false"
VITE_FEATURE_API_ACCESS="false"
VITE_FEATURE_ENTERPRISE_SSO="false"

# Experimental Features
VITE_FEATURE_VOICE_COMMANDS="false"
VITE_FEATURE_MOBILE_APP="true"
VITE_FEATURE_OFFLINE_MODE="true"

# =============================================================================
# STORAGE & CACHING
# =============================================================================

# Local Storage
VITE_STORAGE_QUOTA_MB="100"
VITE_STORAGE_CLEANUP_DAYS="30"
VITE_STORAGE_COMPRESSION="true"

# Cache Configuration
VITE_CACHE_ENABLED="true"
VITE_CACHE_TTL="3600"
VITE_CACHE_MAX_SIZE="50"

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Server
VITE_DEV_SERVER_PORT="5173"
VITE_DEV_SERVER_HOST="localhost"
VITE_DEV_SERVER_HTTPS="false"

# Debug Settings
VITE_DEBUG_MODE="false"
VITE_VERBOSE_LOGGING="false"
VITE_SOURCE_MAPS="false"

# Hot Module Replacement
VITE_HMR_ENABLED="true"
VITE_HMR_PORT="24678"

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# GitHub Integration
VITE_GITHUB_CLIENT_ID="your_github_client_id"
VITE_GITHUB_REDIRECT_URI="https://code-guardian-report.vercel.app/auth/github"

# Slack Integration
VITE_SLACK_WEBHOOK_URL="your_slack_webhook_url"
VITE_SLACK_CHANNEL="#security-alerts"

# Email Notifications
VITE_EMAIL_SERVICE_ENABLED="false"
VITE_EMAIL_FROM="<EMAIL>"
VITE_EMAIL_SMTP_HOST="smtp.gmail.com"
VITE_EMAIL_SMTP_PORT="587"

# =============================================================================
# COMPLIANCE & LEGAL
# =============================================================================

# Privacy Compliance
VITE_GDPR_COMPLIANCE="true"
VITE_CCPA_COMPLIANCE="true"
VITE_COOKIE_CONSENT="true"

# Terms & Conditions
VITE_TERMS_VERSION="1.0"
VITE_PRIVACY_VERSION="1.0"
VITE_LAST_UPDATED="2025-01-27"

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================

# Bundle Optimization
VITE_BUNDLE_ANALYZER="false"
VITE_TREE_SHAKING="true"
VITE_CODE_SPLITTING="true"

# Image Optimization
VITE_IMAGE_OPTIMIZATION="true"
VITE_WEBP_SUPPORT="true"
VITE_LAZY_LOADING="true"

# Network Optimization
VITE_PRELOAD_CRITICAL="true"
VITE_PREFETCH_RESOURCES="true"
VITE_SERVICE_WORKER="true"

# =============================================================================
# ENTERPRISE FEATURES
# =============================================================================

# Multi-tenancy
VITE_MULTI_TENANT="false"
VITE_TENANT_ISOLATION="strict"

# Enterprise Security
VITE_ENTERPRISE_AUDIT_LOG="false"
VITE_ENTERPRISE_BACKUP="false"
VITE_ENTERPRISE_ENCRYPTION="false"

# Scalability
VITE_LOAD_BALANCING="false"
VITE_AUTO_SCALING="false"
VITE_CDN_ENABLED="true"

# =============================================================================
# LOCALIZATION
# =============================================================================

# Internationalization
VITE_I18N_ENABLED="false"
VITE_DEFAULT_LOCALE="en-US"
VITE_SUPPORTED_LOCALES="en-US,es-ES,fr-FR,de-DE,ja-JP,zh-CN"

# Regional Settings
VITE_TIMEZONE="UTC"
VITE_DATE_FORMAT="YYYY-MM-DD"
VITE_CURRENCY="USD"