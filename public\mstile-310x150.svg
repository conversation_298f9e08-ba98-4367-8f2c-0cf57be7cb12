<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 310 150" width="310" height="150">
  <defs>
    <linearGradient id="shieldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" /> <!-- blue-600 -->
      <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" /> <!-- indigo-600 -->
    </linearGradient>
    <linearGradient id="highlightGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>
  </defs>
  <!-- Centered design for wide tile -->
  <rect x="120" y="15" width="70" height="70" rx="15" fill="url(#shieldGrad)" filter="drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))" />
  
  <!-- Shield icon in white -->
  <path d="M155 25 L135 35 L135 55 Q135 65 155 75 Q175 65 175 55 L175 35 Z" fill="white" />
  
  <!-- Highlight overlay for 3D effect -->
  <rect x="120" y="15" width="70" height="70" rx="15" fill="url(#highlightGrad)" opacity="0" />
</svg>