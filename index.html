
<!DOCTYPE html>
<html lang="en" prefix="og: https://ogp.me/ns#">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    
    <!-- Primary Meta Tags -->
    <title>Code Guardian Enterprise - AI-Powered Security Analysis Platform | Professional Code Security</title>
    <meta name="title" content="Code Guardian Enterprise - AI-Powered Security Analysis Platform | Professional Code Security" />
    <meta name="description" content="Enterprise-grade static code analysis platform powered by artificial intelligence. Comprehensive security assessments, vulnerability detection, OWASP compliance, and automated remediation for mission-critical applications. Trusted by 500+ enterprises worldwide." />
    <meta name="author" content="Code Guardian Enterprise Team" />
    <meta name="keywords" content="enterprise security, code analysis, vulnerability detection, static analysis, security compliance, AI security, OWASP compliance, SAST, security scanning, code review, DevSecOps, security automation, enterprise software, security platform, vulnerability management, secure coding, application security" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="rating" content="general" />
    <meta name="distribution" content="global" />
    <meta name="copyright" content="© 2025 Code Guardian Enterprise. All rights reserved." />
    
    <!-- Advanced SEO Meta Tags -->
    <meta name="application-name" content="Code Guardian Enterprise" />
    <meta name="msapplication-TileColor" content="#1e293b" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="HandheldFriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    
    <!-- Security Headers (Note: These should ideally be set as HTTP headers by the server) -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

    <!-- Professional Favicon System -->
    <link rel="icon" type="image/svg+xml" href="/shield-favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="192x192" href="/favicon-192x192.svg" />
    <link rel="icon" type="image/svg+xml" sizes="512x512" href="/favicon-512x512.svg" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#1e293b" />
    <link rel="shortcut icon" href="/favicon.ico" />
    
    <!-- Mobile & PWA Optimization -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Code Guardian" />
    <meta name="apple-touch-fullscreen" content="yes" />
    
    <!-- Performance & Resource Hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://vercel.live" crossorigin />
    <link rel="preconnect" href="https://vitals.vercel-insights.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    
    <!-- Preload Critical Resources -->
    <!-- Fonts will be loaded on demand to avoid preload warnings -->
    
    <!-- SEO & Crawling -->
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
    <meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1" />
    <link rel="canonical" href="https://code-guardian-report.vercel.app/" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Code Guardian Enterprise" />
    <meta property="og:title" content="Code Guardian Enterprise - AI-Powered Security Analysis Platform" />
    <meta property="og:description" content="Enterprise-grade static code analysis platform with comprehensive security assessments, AI-powered vulnerability detection, and automated remediation. Trusted by 500+ enterprises worldwide for mission-critical application security." />
    <meta property="og:image" content="https://code-guardian-report.vercel.app/og-image.png" />
    <meta property="og:image:alt" content="Code Guardian Enterprise - Professional Security Analysis Dashboard" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:url" content="https://code-guardian-report.vercel.app/" />
    <meta property="og:locale" content="en_US" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@CodeGuardianAI" />
    <meta name="twitter:creator" content="@CodeGuardianAI" />
    <meta name="twitter:title" content="Code Guardian Enterprise - AI-Powered Security Analysis Platform" />
    <meta name="twitter:description" content="Enterprise-grade static code analysis platform with comprehensive security assessments and AI-powered vulnerability detection for modern development teams." />
    <meta name="twitter:image" content="https://code-guardian-report.vercel.app/twitter-card.png" />
    <meta name="twitter:image:alt" content="Code Guardian Enterprise - Professional Security Analysis Platform" />
    
    <!-- LinkedIn -->
    <meta property="og:image:secure_url" content="https://code-guardian-report.vercel.app/linkedin-card.png" />
    
    <!-- Professional Brand Colors & Theme -->
    <meta name="theme-color" content="#1e293b" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#f8fafc" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#1e293b" />
    <meta name="color-scheme" content="light dark" />
    
    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/mstile-144x144.png" />
    <meta name="msapplication-square70x70logo" content="/mstile-70x70.png" />
    <meta name="msapplication-square150x150logo" content="/mstile-150x150.png" />
    <meta name="msapplication-wide310x150logo" content="/mstile-310x150.png" />
    <meta name="msapplication-square310x310logo" content="/mstile-310x310.png" />
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Sitemap -->
    <link rel="sitemap" type="application/xml" href="/sitemap.xml" />
    
    <!-- RSS Feed -->
    <link rel="alternate" type="application/rss+xml" title="Code Guardian Enterprise Updates" href="/feed.xml" />
    
    <!-- Structured Data Preload -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Code Guardian Enterprise",
      "url": "https://code-guardian-report.vercel.app",
      "logo": "https://code-guardian-report.vercel.app/logo-512.png",
      "description": "Enterprise-grade static code analysis platform powered by artificial intelligence",
      "foundingDate": "2024",
      "industry": "Cybersecurity Software",
      "numberOfEmployees": "10-50",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "Customer Service",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://github.com/Xenonesis/code-guardian-report",
        "https://linkedin.com/company/code-guardian",
        "https://twitter.com/CodeGuardianAI"
      ]
    }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
  
  <!-- Professional Schema.org markup for SEO -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org/",
    "@type": "SoftwareApplication",
    "name": "Code Guardian Enterprise",
    "alternateName": "Code Guardian",
    "operatingSystem": "Windows, Linux, macOS",
    "applicationCategory": "SecurityApplication",
    "applicationSubCategory": "Static Code Analysis",
    "description": "Enterprise-grade static code analysis platform powered by artificial intelligence. Comprehensive security assessments, vulnerability detection, and compliance reporting for mission-critical applications.",
    "keywords": "enterprise security, code analysis, vulnerability detection, static analysis, security compliance, AI security",
    "targetAudience": {
      "@type": "Audience",
      "audienceType": "Enterprise Developers"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "500",
      "bestRating": "5",
      "worstRating": "1"
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Enterprise Security Framework",
      "AI-Powered Analysis",
      "OWASP Compliance",
      "Vulnerability Detection",
      "Compliance Reporting"
    ]
  }
  </script>
</html>
