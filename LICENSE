MIT License with Additional International and Educational Use Provisions

Copyright (c) 2025 <PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

================================================================================
ADDITIONAL TERMS AND CONDITIONS FOR INTERNATIONAL AND EDUCATIONAL USE
================================================================================

1. EDUCATIONAL USE PROVISIONS
   a) This software is explicitly made available for educational purposes without
      any additional restrictions or fees.
   b) Educational institutions, students, teachers, and researchers may use,
      modify, and distribute this software for non-commercial educational
      purposes.
   c) Educational use includes but is not limited to: classroom instruction,
      academic research, student projects, thesis work, and educational
      demonstrations.

2. ATTRIBUTION REQUIREMENTS
   a) All copies, modifications, or derivative works must prominently display:
      "Original Code Guardian project created by Aditya Kumar Tiwari"
   b) Any public presentation, publication, or distribution must include proper
      attribution to the original author.
   c) Commercial derivatives must include attribution in user-facing
      documentation and about sections.

3. INTERNATIONAL COMPLIANCE

   3.1 INDIA SPECIFIC PROVISIONS
   a) This software complies with the Information Technology Act, 2000 and
      subsequent amendments.
   b) Users in India must comply with the Personal Data Protection Bill
      requirements when processing personal data.
   c) Commercial use in India must comply with GST regulations and applicable
      tax laws.
   d) Any disputes arising in India shall be governed by Indian law and subject
      to the jurisdiction of Indian courts.

   3.2 EUROPEAN UNION PROVISIONS
   a) This software is GDPR (General Data Protection Regulation) compliant for
      data processing activities.
   b) Users processing personal data of EU residents must implement appropriate
      technical and organizational measures.
   c) Data subjects' rights under GDPR (access, rectification, erasure,
      portability) must be respected.
   d) Any disputes in the EU shall be subject to the jurisdiction of the
      relevant EU member state courts.

   3.3 UNITED STATES PROVISIONS
   a) This software complies with applicable US federal and state laws.
   b) Export control regulations (EAR/ITAR) must be observed for international
      distribution.
   c) Users must comply with state-specific privacy laws (CCPA, etc.) where
      applicable.

   3.4 GENERAL INTERNATIONAL PROVISIONS
   a) Users must comply with all applicable local laws and regulations in their
      jurisdiction.
   b) This license does not grant rights that conflict with local mandatory laws.
   c) If any provision is deemed invalid in a jurisdiction, the remainder
      remains enforceable.

4. OPEN SOURCE COMMITMENT
   a) This project is committed to remaining free and open source.
   b) Any modifications or improvements are encouraged to be contributed back
      to the community.
   c) Commercial entities benefiting from this software are encouraged to
      support its continued development.

5. SECURITY AND LIABILITY
   a) Users are responsible for ensuring the security of their implementations.
   b) This software is provided for code analysis purposes and should not be
      the sole security measure.
   c) The author is not liable for any security vulnerabilities in user code
      that this software fails to detect.

6. COMMUNITY GUIDELINES
   a) This software should be used in accordance with ethical coding practices.
   b) Users should respect intellectual property rights of analyzed code.
   c) The software should not be used for malicious purposes or to harm others.

7. UPDATES AND MODIFICATIONS
   a) The author reserves the right to update these additional terms with
      reasonable notice.
   b) Continued use after updates constitutes acceptance of new terms.
   c) Users may continue using previous versions under the original terms.

8. CONTACT AND SUPPORT
   For questions regarding this license or compliance requirements:
   - Author: Aditya Kumar Tiwari
   - Project: Code Guardian (https://github.com/Xenonesis/code-guardian-report)
   - Issues: https://github.com/Xenonesis/code-guardian-report/issues

================================================================================
ACKNOWLEDGMENT
================================================================================

By using this software, you acknowledge that you have read, understood, and
agree to be bound by both the MIT License terms above and these additional
international and educational use provisions.

This enhanced license ensures global compliance while maintaining the open
source nature of the project and supporting educational use worldwide.

Last Updated: June 2025
